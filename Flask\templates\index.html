<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ứng dụng đa năng</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <div id="app">
        <header>
            <div class="logo">
                <button @click="showProgramList = !showProgramList" class="program-list-button">
                    <i class="fas fa-th"></i> <PERSON><PERSON><PERSON><PERSON> trình
                </button>
                <h1>Ứng dụng đa năng</h1>
            </div>
            <div class="user-info">
                <span>Phiên bản 1.0</span>
            </div>
        </header>

        <!-- Danh sách chương trình -->
        <div class="program-list" v-show="showProgramList">
            <div class="program-list-header">
                <h3>Danh sách chương trình</h3>
                <button @click="showProgramList = false" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="program-items">
                <div v-for="(program, id) in availablePrograms" :key="id"
                    class="program-item" @click="openProgram(id)">
                    <img :src="'/static/img/' + program.icon" alt="Program Icon">
                    <div class="program-info">
                        <h4>[[ program.name ]]</h4>
                        <p>[[ program.description ]]</p>
                    </div>
                </div>
                <div v-if="Object.keys(availablePrograms).length === 0" class="no-programs">
                    Tất cả các chương trình đã mở
                </div>
            </div>
        </div>

        <!-- Nội dung chính với các tab -->
        <main>
            <!-- Tab bar -->
            <div class="tab-bar">
                <div v-for="(tab, index) in tabs" :key="index"
                    :class="['tab', { active: currentTab === index }]"
                    @click="switchTab(index)">
                    <span>[[ tab.name ]]</span>
                    <button @click.stop="closeTab(index)" class="tab-close">×</button>
                </div>
            </div>

            <!-- Nội dung tab -->
            <div class="tab-content">
                <div v-for="(tab, index) in tabs" :key="index"
                    :class="['tab-pane', { active: currentTab === index }]">
                    <iframe :src="tab.url" frameborder="0" class="program-frame"
                        :id="'frame-' + tab.id"></iframe>
                </div>

                <div v-if="tabs.length === 0" class="welcome-screen">
                    <div class="welcome-content">
                        <h2>Chào mừng đến với ứng dụng đa năng</h2>
                        <p>Chọn một chương trình từ danh sách ở góc trái trên để bắt đầu</p>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <div class="status-bar">
                <span>Sẵn sàng</span>
            </div>
        </footer>
    </div>

    <script>
        // Cấu hình Vue để sử dụng cú pháp [[ ]] thay vì {{ }}
        // Vue.config.delimiters = ['[[', ']]'];

        new Vue({
            el: '#app',
            delimiters: ['[[', ']]'],
            data: {
                showProgramList: false,
                tabs: [],
                currentTab: -1,
                availablePrograms: {}
            },
            mounted() {
                this.loadAvailablePrograms();
            },
            methods: {
                loadAvailablePrograms() {
                    axios.get('/api/programs')
                        .then(response => {
                            this.availablePrograms = response.data;
                        })
                        .catch(error => {
                            console.error('Lỗi khi tải danh sách chương trình:', error);
                        });
                },
                openProgram(programId) {
                    const program = this.availablePrograms[programId];
                    if (!program) return;

                    // Tạo tab mới
                    const tab = {
                        id: programId,
                        name: program.name,
                        url: `/program/${programId}`
                    };

                    // Thêm tab và chuyển đến tab đó
                    this.tabs.push(tab);
                    this.currentTab = this.tabs.length - 1;

                    // Ẩn chương trình đã mở khỏi danh sách
                    delete this.availablePrograms[programId];

                    // Đóng menu danh sách
                    this.showProgramList = false;
                },
                switchTab(index) {
                    this.currentTab = index;
                },
                closeTab(index) {
                    const tab = this.tabs[index];

                    // Thông báo cho server rằng chương trình đã đóng
                    axios.post('/api/close_program', { program_id: tab.id })
                        .then(() => {
                            // Tải lại danh sách chương trình có sẵn
                            this.loadAvailablePrograms();
                        })
                        .catch(error => {
                            console.error('Lỗi khi đóng chương trình:', error);
                        });

                    // Xóa tab
                    this.tabs.splice(index, 1);

                    // Nếu tab hiện tại bị xóa, chuyển đến tab trước đó
                    if (this.currentTab === index) {
                        if (index > 0) {
                            this.currentTab = index - 1;
                        } else if (this.tabs.length > 0) {
                            this.currentTab = 0;
                        } else {
                            this.currentTab = -1;
                        }
                    } else if (this.currentTab > index) {
                        // Nếu tab hiện tại nằm sau tab bị xóa, điều chỉnh chỉ số
                        this.currentTab--;
                    }
                }
            }
        });
    </script>
</body>
</html>