from flask import Flask, render_template, jsonify, request, session
import os
import importlib
import sys
from pathlib import Path

app = Flask(__name__)
app.secret_key = 'your_secret_key_here'  # <PERSON><PERSON><PERSON> thành một secret key an toàn hơn

# Đ<PERSON>ng ký c<PERSON>c program module
def register_programs():
    programs_dir = Path(__file__).parent / 'programs'
    if not programs_dir.exists():
        return

    app.programs = {}
    for item in programs_dir.iterdir():
        if item.is_dir() and (item / '__init__.py').exists():
            program_name = item.name
            try:
                module_path = f"programs.{program_name}"
                module = importlib.import_module(module_path)
                if hasattr(module, 'setup'):
                    module.setup(app)
                app.programs[program_name] = {
                    'name': getattr(module, 'NAME', program_name),
                    'description': getattr(module, 'DESCRIPTION', ''),
                    'icon': getattr(module, 'ICON', 'default-icon.png'),
                    'active': False
                }
            except Exception as e:
                print(f"Không thể nạp chương trình {program_name}: {str(e)}")

# Gọi register_programs khi khởi tạo app
register_programs()

@app.route('/')
def index():
    return render_template('index.html', programs=getattr(app, 'programs', {}))

@app.route('/program/<program_id>')
def program_ui(program_id):
    if not hasattr(app, 'programs') or program_id not in app.programs:
        return "Chương trình không tồn tại", 404

    app.programs[program_id]['active'] = True
    session['active_programs'] = session.get('active_programs', [])
    if program_id not in session['active_programs']:
        session['active_programs'].append(program_id)

    # Trả về nội dung UI của chương trình
    return render_template(f'programs/{program_id}.html')

@app.route('/api/programs')
def get_programs():
    if not hasattr(app, 'programs'):
        return jsonify({})

    active_programs = session.get('active_programs', [])
    available_programs = {
        k: v for k, v in app.programs.items()
        if k not in active_programs
    }
    return jsonify(available_programs)

@app.route('/api/close_program', methods=['POST'])
def close_program():
    program_id = request.json.get('program_id')
    if hasattr(app, 'programs') and program_id in app.programs:
        app.programs[program_id]['active'] = False
        if 'active_programs' in session and program_id in session['active_programs']:
            session['active_programs'].remove(program_id)
    return jsonify({"success": True})

if __name__ == '__main__':
    print("🚀 Starting Flask app...")
    print("📁 Programs loaded:", getattr(app, 'programs', {}))
    print("🌐 Server will start at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)