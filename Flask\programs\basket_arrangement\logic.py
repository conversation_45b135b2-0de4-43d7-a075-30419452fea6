import sys
import random
import re
import time
import json
import base64
from pathlib import Path

# Import gsheet_manager từ thư mục gốc
sys.path.append(str(Path(__file__).parent.parent.parent))
from gsheet_manager import GoogleSheetManager

# Import các class và hàm từ basket_package
sys.path.append(str(Path(__file__).parent.parent.parent))
from basket_package import (
    SmartPlacementEngine, normalize_timeline, col_to_index, index_to_col,
    MAX_UNIQUE_IDS, DEFAULT_SHEET_NAME, NUM_ID_COLUMNS, COLUMN_STEP,
    TIME_SLOT_ROW, HOUR_NAME_ROW, HEADER_ROW, DATA_START_ROW,
    DEAL_LIST_HEADER_ROW, DEAL_LIST_DATA_START_ROW,
    MIN_DISTANCE_BETWEEN_INPUT_IDS, PLACEMENT_PRIORITY_WEIGHTS,
    BASE64_OAUTH
)

class DealListManager:
    """Quản lý dữ liệu Deal list - chuyển đổi từ PyQt6 sang pure Python"""

    def __init__(self):
        self.id_to_shop = {}
        self.id_to_gmv = {}
        self.id_to_review = {}
        self.id_to_no = {}
        self.shop_to_ids = {}
        self.column_mapping = {
            'shop_id': 'A',
            'item_id': 'B',
            'gmv': 'C',
            'review': 'D',
            'no': 'E'
        }

    def load_deal_list_data(self, sheet_manager, spreadsheet, sheet_name, mapping=None):
        """Load dữ liệu từ Deal list sheet"""
        if mapping:
            self.column_mapping = mapping

        try:
            # Sử dụng spreadsheet đã được truyền vào thay vì mở lại
            worksheet = None
            for ws in spreadsheet.worksheets():
                if ws.title == sheet_name:
                    worksheet = ws
                    break

            if not worksheet:
                # Nếu không tìm thấy sheet với tên chính xác, sử dụng sheet đầu tiên
                worksheet = spreadsheet.get_worksheet(0)

            # Lấy tất cả dữ liệu
            all_values = worksheet.get_all_values()

            if len(all_values) < DEAL_LIST_DATA_START_ROW:
                return False

            # Xử lý từng dòng dữ liệu
            for row_idx in range(DEAL_LIST_DATA_START_ROW - 1, len(all_values)):
                row = all_values[row_idx]

                # Lấy dữ liệu theo mapping
                shop_id = self._get_cell_value(row, self.column_mapping['shop_id'])
                item_id = self._get_cell_value(row, self.column_mapping['item_id'])
                gmv_str = self._get_cell_value(row, self.column_mapping['gmv'])
                review_str = self._get_cell_value(row, self.column_mapping['review'])
                no_str = self._get_cell_value(row, self.column_mapping['no'])

                if not item_id:
                    continue

                # Xử lý GMV
                try:
                    gmv = float(gmv_str.replace(',', '')) if gmv_str else 0.0
                except:
                    gmv = 0.0

                # Xử lý Review
                has_review = bool(review_str and review_str.strip())

                # Xử lý NO
                try:
                    no = int(no_str) if no_str else 999999
                except:
                    no = 999999

                # Lưu vào dictionary
                self.id_to_shop[item_id] = shop_id
                self.id_to_gmv[item_id] = gmv
                self.id_to_review[item_id] = has_review
                self.id_to_no[item_id] = no

                # Nhóm theo shop
                if shop_id not in self.shop_to_ids:
                    self.shop_to_ids[shop_id] = []
                self.shop_to_ids[shop_id].append(item_id)

            return True

        except Exception as e:
            raise Exception(f"Lỗi khi load Deal list: {str(e)}")

    def _get_cell_value(self, row, col_letter):
        """Lấy giá trị cell theo chữ cái cột"""
        col_index = col_to_index(col_letter)
        if col_index < len(row):
            return row[col_index]
        return ""

    def has_review(self, item_id):
        """Kiểm tra ID có review không"""
        return self.id_to_review.get(item_id, False)

    def get_gmv(self, item_id):
        """Lấy GMV của ID"""
        return self.id_to_gmv.get(item_id, 0.0)

    def get_no(self, item_id):
        """Lấy NO của ID"""
        return self.id_to_no.get(item_id, 999999)

class TimeSlotProcessor:
    """Xử lý logic cho từng khung giờ"""

    def __init__(self, deal_list_manager, log_function=None):
        self.deal_list_manager = deal_list_manager
        self.log = log_function or print
        self.smart_engine = SmartPlacementEngine(deal_list_manager, log_function)

    def process_time_slot(self, current_ids, input_conditions, timeline):
        """Xử lý sắp xếp cho một khung giờ"""
        self.log(f"\n=== XỬ LÝ KHUNG GIỜ: {timeline} ===")

        # Lọc điều kiện cho timeline này
        relevant_conditions = []
        for condition in input_conditions:
            top_limit, input_ids, selected_times, is_grouped = condition
            if timeline in selected_times:
                relevant_conditions.append(condition)

        if not relevant_conditions:
            self.log(f"Không có điều kiện nào cho khung giờ {timeline}")
            return current_ids

        # Xử lý từng điều kiện
        result_ids = current_ids.copy()

        for condition in relevant_conditions:
            top_limit_str, input_ids, selected_times, is_grouped = condition

            # Parse top limit
            top_limit = self._parse_top_limit(top_limit_str)

            self.log(f"Xử lý điều kiện: {len(input_ids)} ID, Top {top_limit}, Nhóm: {is_grouped}")

            # Áp dụng thuật toán sắp xếp
            result_ids = self._apply_arrangement_algorithm(
                result_ids, input_ids, top_limit, is_grouped
            )

        return result_ids

    def _parse_top_limit(self, top_limit_str):
        """Parse chuỗi top limit thành số"""
        # Ví dụ: "Top 20" -> 20, "Top 150↓" -> 150
        match = re.search(r'(\d+)', top_limit_str)
        if match:
            return int(match.group(1))
        return 100  # Default

    def _apply_arrangement_algorithm(self, current_ids, input_ids, top_limit, is_grouped):
        """Áp dụng thuật toán sắp xếp ID"""
        # Tạo dictionary cho placement engine
        top_limits = {id_val: top_limit for id_val in input_ids}
        is_grouped_flags = {id_val: is_grouped for id_val in input_ids}

        # Sử dụng Smart Placement Engine
        placements = self.smart_engine.find_optimal_positions(
            current_ids, input_ids, top_limits, is_grouped_flags
        )

        # Áp dụng placements
        result_ids = current_ids.copy()

        for id_val, position, placement_type in placements:
            # Chèn ID vào vị trí
            if position >= len(result_ids):
                result_ids.extend([''] * (position - len(result_ids) + 1))

            result_ids.insert(position, id_val)
            self.log(f"Chèn {id_val} vào vị trí {position} ({placement_type})")

        # Giới hạn số lượng ID theo MAX_UNIQUE_IDS
        if len(result_ids) > MAX_UNIQUE_IDS:
            result_ids = result_ids[:MAX_UNIQUE_IDS]
            self.log(f"Giới hạn danh sách xuống {MAX_UNIQUE_IDS} ID")

        return result_ids

    def get_avoid_positions(self, top_limit):
        """Tính avoid positions dựa trên top limit"""
        if top_limit == 20: return 3
        elif top_limit == 30: return 6
        elif top_limit == 50: return 10
        elif top_limit == 100: return 20
        elif top_limit == 150: return 30
        elif top_limit == 200: return 100
        elif top_limit == 250: return 120
        else: return top_limit // 5

class BasketArrangementLogic:
    """Logic chính cho Basket Arrangement"""

    def __init__(self):
        self.sheet_manager = None
        self.spreadsheet = None
        self.deal_list_manager = DealListManager()
        self.time_slot_processor = None
        self.logs = []

    def log(self, message):
        """Ghi log"""
        print(message)
        self.logs.append(message)

    def load_spreadsheet(self, sheet_url):
        """Load Google Spreadsheet"""
        try:
            # Extract spreadsheet ID từ URL
            sheet_id = self._extract_sheet_id(sheet_url)

            # Khởi tạo GoogleSheetManager với OAuth
            self.sheet_manager = GoogleSheetManager(
                auth_type='oauth',
                credentials_data=BASE64_OAUTH
            )

            # Mở spreadsheet
            self.spreadsheet = self.sheet_manager.open_by_key(sheet_id)

            # Lấy danh sách sheets
            worksheets = self.spreadsheet.worksheets()
            sheet_names = [ws.title for ws in worksheets]

            self.log(f"Đã load spreadsheet thành công: {len(sheet_names)} sheets")

            return {
                'sheet_id': sheet_id,
                'sheet_names': sheet_names,
                'total_sheets': len(sheet_names)
            }

        except Exception as e:
            raise Exception(f"Lỗi khi load spreadsheet: {str(e)}")

    def _extract_sheet_id(self, url):
        """Trích xuất sheet ID từ URL"""
        patterns = [
            r'/spreadsheets/d/([a-zA-Z0-9-_]+)',
            r'key=([a-zA-Z0-9-_]+)',
            r'^([a-zA-Z0-9-_]+)$'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        raise ValueError("Không thể trích xuất Sheet ID từ URL")

    def get_time_slots(self, sheet_name, first_col, num_columns):
        """Lấy danh sách khung giờ từ sheet"""
        try:
            worksheet = self.spreadsheet.worksheet(sheet_name)
            time_slots = []

            for i in range(num_columns):
                col_index = col_to_index(first_col) + (i * COLUMN_STEP)
                col_letter = index_to_col(col_index)

                # Lấy giá trị khung giờ từ TIME_SLOT_ROW
                cell_value = worksheet.cell(TIME_SLOT_ROW, col_index + 1).value
                if cell_value and cell_value.strip():
                    time_slots.append(cell_value.strip())

            return time_slots

        except Exception as e:
            raise Exception(f"Lỗi khi lấy khung giờ: {str(e)}")

    def get_default_column_mapping(self):
        """Lấy mapping cột mặc định"""
        return {
            'shop_id': 'A',
            'item_id': 'B',
            'gmv': 'C',
            'review': 'D',
            'no': 'E'
        }

    def process_arrangement(self, conditions, sheet_config):
        """Xử lý sắp xếp ID chính"""
        try:
            # Load Deal list data
            deal_sheet_name = sheet_config.get('deal_sheet_name', 'Deal list')
            mapping = sheet_config.get('column_mapping', self.get_default_column_mapping())

            self.deal_list_manager.load_deal_list_data(
                self.sheet_manager, self.spreadsheet, deal_sheet_name, mapping
            )

            # Khởi tạo time slot processor
            self.time_slot_processor = TimeSlotProcessor(
                self.deal_list_manager, self.log
            )

            # Lấy thông tin sheet Basket
            basket_sheet_name = sheet_config.get('basket_sheet_name', DEFAULT_SHEET_NAME)
            first_col = sheet_config.get('first_col', 'A')
            num_columns = sheet_config.get('num_columns', NUM_ID_COLUMNS)

            # Lấy khung giờ
            time_slots = self.get_time_slots(basket_sheet_name, first_col, num_columns)

            # Xử lý từng khung giờ
            results = {}
            worksheet = self.spreadsheet.worksheet(basket_sheet_name)

            for i, time_slot in enumerate(time_slots):
                col_index = col_to_index(first_col) + (i * COLUMN_STEP) + 1  # Item ID column

                # Lấy dữ liệu hiện tại
                current_ids = self._get_current_ids(worksheet, col_index)

                # Xử lý sắp xếp
                new_ids = self.time_slot_processor.process_time_slot(
                    current_ids, conditions, time_slot
                )

                # Cập nhật vào sheet
                self._update_ids_to_sheet(worksheet, col_index, new_ids)

                results[time_slot] = {
                    'original_count': len([id for id in current_ids if id]),
                    'new_count': len([id for id in new_ids if id]),
                    'added_count': len([id for id in new_ids if id]) - len([id for id in current_ids if id])
                }

            return {
                'message': 'Đã xử lý sắp xếp thành công',
                'results': results,
                'logs': self.logs
            }

        except Exception as e:
            raise Exception(f"Lỗi khi xử lý sắp xếp: {str(e)}")

    def _get_current_ids(self, worksheet, col_index):
        """Lấy danh sách ID hiện tại từ cột"""
        try:
            # Lấy dữ liệu từ DATA_START_ROW đến cuối
            values = worksheet.col_values(col_index)

            # Bỏ qua header rows
            if len(values) > DATA_START_ROW - 1:
                current_ids = values[DATA_START_ROW - 1:]
            else:
                current_ids = []

            # Làm sạch dữ liệu
            cleaned_ids = []
            for id_val in current_ids:
                if id_val and str(id_val).strip():
                    cleaned_ids.append(str(id_val).strip())
                else:
                    cleaned_ids.append('')

            return cleaned_ids

        except Exception as e:
            self.log(f"Lỗi khi lấy ID hiện tại: {str(e)}")
            return []

    def _update_ids_to_sheet(self, worksheet, col_index, new_ids):
        """Cập nhật danh sách ID mới vào sheet"""
        try:
            # Chuẩn bị dữ liệu để update
            update_data = []
            for i, id_val in enumerate(new_ids):
                row_num = DATA_START_ROW + i
                update_data.append([id_val if id_val else ''])

            # Update vào sheet
            if update_data:
                range_name = f"{index_to_col(col_index - 1)}{DATA_START_ROW}:{index_to_col(col_index - 1)}{DATA_START_ROW + len(update_data) - 1}"
                worksheet.update(range_name, update_data)

                self.log(f"Đã cập nhật {len(update_data)} ID vào cột {index_to_col(col_index - 1)}")

        except Exception as e:
            self.log(f"Lỗi khi cập nhật ID: {str(e)}")

# Thêm các class hỗ trợ khác từ basket_package
class ExclusiveModeManager:
    """Quản lý chế độ Exclusive"""

    def __init__(self):
        self.exclusive_ids = set()
        self.is_enabled = False

    def enable(self, exclusive_ids):
        """Bật chế độ exclusive với danh sách ID"""
        self.is_enabled = True
        self.exclusive_ids = set(exclusive_ids)

    def disable(self):
        """Tắt chế độ exclusive"""
        self.is_enabled = False
        self.exclusive_ids.clear()

    def should_preserve_id(self, item_id):
        """Kiểm tra ID có nên được giữ nguyên không"""
        return self.is_enabled and item_id in self.exclusive_ids

class AdvancedSortingEngine:
    """Engine sắp xếp nâng cao theo thuật toán v2.0"""

    def __init__(self, deal_list_manager, log_function=None):
        self.deal_list_manager = deal_list_manager
        self.log = log_function or print

    def sort_ids_by_no_and_review(self, id_list):
        """
        Sắp xếp ID theo thuật toán v2.0:
        1. Thứ tự NO là "bất khả xâm phạm"
        2. Trong cùng NO: ID có review trước, không review sau
        3. Trong cùng NO và review: sắp xếp theo GMV giảm dần
        """
        if not id_list:
            return []

        # Lọc bỏ ID trống
        valid_ids = [id_val for id_val in id_list if id_val and id_val.strip()]

        if not valid_ids:
            return []

        self.log(f"Sắp xếp {len(valid_ids)} ID theo thuật toán v2.0")

        # Tạo danh sách với thông tin để sắp xếp
        id_info_list = []
        for id_val in valid_ids:
            no = self.deal_list_manager.get_no(id_val)
            has_review = self.deal_list_manager.has_review(id_val)
            gmv = self.deal_list_manager.get_gmv(id_val)

            id_info_list.append({
                'id': id_val,
                'no': no,
                'has_review': has_review,
                'gmv': gmv
            })

        # Sắp xếp theo thứ tự ưu tiên:
        # 1. NO tăng dần (thứ tự NO là bất khả xâm phạm)
        # 2. Review giảm dần (có review trước, không review sau)
        # 3. GMV giảm dần
        sorted_list = sorted(id_info_list, key=lambda x: (
            x['no'],           # NO tăng dần
            not x['has_review'], # Review: True trước False
            -x['gmv']          # GMV giảm dần
        ))

        # Trả về danh sách ID đã sắp xếp
        result = [item['id'] for item in sorted_list]

        self.log(f"✅ Đã sắp xếp {len(result)} ID theo NO và Review")
        return result

class ValidationEngine:
    """Engine kiểm tra và validation"""

    def __init__(self, deal_list_manager, log_function=None):
        self.deal_list_manager = deal_list_manager
        self.log = log_function or print

    def validate_id_list(self, id_list):
        """Kiểm tra tính hợp lệ của danh sách ID"""
        if not id_list:
            return True, "Danh sách trống"

        issues = []
        valid_count = 0

        for i, id_val in enumerate(id_list):
            if not id_val or not id_val.strip():
                continue

            valid_count += 1

            # Kiểm tra ID có tồn tại trong Deal list không
            if id_val not in self.deal_list_manager.id_to_shop:
                issues.append(f"ID {id_val} không tồn tại trong Deal list")

        if issues:
            self.log(f"⚠️ Phát hiện {len(issues)} vấn đề trong danh sách ID")
            for issue in issues[:5]:  # Chỉ hiển thị 5 lỗi đầu tiên
                self.log(f"  - {issue}")
            if len(issues) > 5:
                self.log(f"  - ... và {len(issues) - 5} vấn đề khác")

        return len(issues) == 0, f"Kiểm tra {valid_count} ID, phát hiện {len(issues)} vấn đề"

    def check_no_order_integrity(self, id_list):
        """Kiểm tra tính toàn vẹn của thứ tự NO"""
        if not id_list:
            return True, "Danh sách trống"

        # Lấy danh sách NO của các ID
        no_sequence = []
        for id_val in id_list:
            if id_val and id_val.strip():
                no = self.deal_list_manager.get_no(id_val)
                no_sequence.append(no)

        if not no_sequence:
            return True, "Không có ID hợp lệ"

        # Kiểm tra thứ tự NO có tăng dần không
        is_ordered = all(no_sequence[i] <= no_sequence[i+1] for i in range(len(no_sequence)-1))

        if is_ordered:
            self.log("✅ Thứ tự NO được duy trì đúng")
            return True, "Thứ tự NO hợp lệ"
        else:
            self.log("⚠️ Phát hiện vi phạm thứ tự NO")
            return False, "Thứ tự NO bị vi phạm"

# Cập nhật BasketArrangementLogic để sử dụng các engine mới
class EnhancedBasketArrangementLogic(BasketArrangementLogic):
    """Logic nâng cao với đầy đủ tính năng"""

    def __init__(self):
        super().__init__()
        self.exclusive_manager = ExclusiveModeManager()
        self.sorting_engine = None
        self.validation_engine = None

    def initialize_engines(self):
        """Khởi tạo các engine sau khi load deal list"""
        self.sorting_engine = AdvancedSortingEngine(self.deal_list_manager, self.log)
        self.validation_engine = ValidationEngine(self.deal_list_manager, self.log)
        self.time_slot_processor = EnhancedTimeSlotProcessor(
            self.deal_list_manager, self.log, self.sorting_engine, self.validation_engine
        )

    def process_arrangement(self, conditions, sheet_config):
        """Xử lý sắp xếp ID nâng cao"""
        try:
            # Load Deal list data
            deal_sheet_name = sheet_config.get('deal_sheet_name', 'Deal list')
            mapping = sheet_config.get('column_mapping', self.get_default_column_mapping())

            self.deal_list_manager.load_deal_list_data(
                self.sheet_manager, self.spreadsheet, deal_sheet_name, mapping
            )

            # Khởi tạo các engine
            self.initialize_engines()

            # Lấy thông tin sheet Basket
            basket_sheet_name = sheet_config.get('basket_sheet_name', DEFAULT_SHEET_NAME)
            first_col = sheet_config.get('first_col', 'A')
            num_columns = sheet_config.get('num_columns', NUM_ID_COLUMNS)

            # Lấy khung giờ
            time_slots = self.get_time_slots(basket_sheet_name, first_col, num_columns)

            # Xử lý từng khung giờ
            results = {}
            worksheet = self.spreadsheet.worksheet(basket_sheet_name)

            for i, time_slot in enumerate(time_slots):
                col_index = col_to_index(first_col) + (i * COLUMN_STEP) + 1  # Item ID column

                # Lấy dữ liệu hiện tại
                current_ids = self._get_current_ids(worksheet, col_index)

                # Validation trước khi xử lý
                is_valid, validation_msg = self.validation_engine.validate_id_list(current_ids)
                self.log(f"Validation {time_slot}: {validation_msg}")

                # Xử lý sắp xếp
                new_ids = self.time_slot_processor.process_time_slot(
                    current_ids, conditions, time_slot
                )

                # Validation sau khi xử lý
                is_valid_after, validation_msg_after = self.validation_engine.check_no_order_integrity(new_ids)
                self.log(f"Validation sau xử lý {time_slot}: {validation_msg_after}")

                # Cập nhật vào sheet
                self._update_ids_to_sheet(worksheet, col_index, new_ids)

                results[time_slot] = {
                    'original_count': len([id for id in current_ids if id]),
                    'new_count': len([id for id in new_ids if id]),
                    'added_count': len([id for id in new_ids if id]) - len([id for id in current_ids if id]),
                    'validation_before': validation_msg,
                    'validation_after': validation_msg_after
                }

            return {
                'message': 'Đã xử lý sắp xếp thành công',
                'results': results,
                'logs': self.logs
            }

        except Exception as e:
            raise Exception(f"Lỗi khi xử lý sắp xếp: {str(e)}")

class EnhancedTimeSlotProcessor(TimeSlotProcessor):
    """Time Slot Processor nâng cao"""

    def __init__(self, deal_list_manager, log_function, sorting_engine, validation_engine):
        super().__init__(deal_list_manager, log_function)
        self.sorting_engine = sorting_engine
        self.validation_engine = validation_engine

    def process_time_slot(self, current_ids, input_conditions, timeline):
        """Xử lý sắp xếp nâng cao cho một khung giờ"""
        self.log(f"\n=== XỬ LÝ KHUNG GIỜ NÂNG CAO: {timeline} ===")

        # Lọc điều kiện cho timeline này
        relevant_conditions = []
        for condition in input_conditions:
            top_limit, input_ids, selected_times, is_grouped = condition
            if timeline in selected_times:
                relevant_conditions.append(condition)

        if not relevant_conditions:
            self.log(f"Không có điều kiện nào cho khung giờ {timeline}")
            # Vẫn áp dụng sắp xếp theo NO và Review cho dữ liệu hiện có
            sorted_current = self.sorting_engine.sort_ids_by_no_and_review(current_ids)
            return sorted_current

        # Xử lý từng điều kiện
        result_ids = current_ids.copy()

        for condition in relevant_conditions:
            top_limit_str, input_ids, selected_times, is_grouped = condition

            # Parse top limit
            top_limit = self._parse_top_limit(top_limit_str)

            self.log(f"Xử lý điều kiện: {len(input_ids)} ID, Top {top_limit}, Nhóm: {is_grouped}")

            # Validation input IDs
            is_valid, validation_msg = self.validation_engine.validate_id_list(input_ids)
            self.log(f"Validation input: {validation_msg}")

            # Áp dụng thuật toán sắp xếp
            result_ids = self._apply_enhanced_arrangement_algorithm(
                result_ids, input_ids, top_limit, is_grouped
            )

        # Sắp xếp cuối cùng theo NO và Review
        final_result = self.sorting_engine.sort_ids_by_no_and_review(result_ids)

        return final_result

    def _apply_enhanced_arrangement_algorithm(self, current_ids, input_ids, top_limit, is_grouped):
        """Áp dụng thuật toán sắp xếp nâng cao"""
        # Sử dụng thuật toán từ class cha nhưng với validation
        result = super()._apply_arrangement_algorithm(current_ids, input_ids, top_limit, is_grouped)

        # Kiểm tra và sửa chữa thứ tự NO nếu cần
        is_valid, msg = self.validation_engine.check_no_order_integrity(result)
        if not is_valid:
            self.log("🔧 Đang sửa chữa thứ tự NO...")
            result = self.sorting_engine.sort_ids_by_no_and_review(result)

        return result
