from flask import Blueprint, render_template, jsonify, request

# Thông tin chương trình (sẽ hiển thị trong danh sách)
NAME = "Chương trình mẫu"
DESCRIPTION = "Đây là một chương trình mẫu để minh họa cấu trúc"
ICON = "example_icon.png"

# Tạo blueprint cho chương trình
bp = Blueprint('example_program', __name__, 
               url_prefix='/example_program',
               template_folder='templates')

# Đường dẫn đến logic chính của chương trình
from .logic import calculate_result

@bp.route('/')
def index():
    """Trang chính của chương trình"""
    return render_template('example_program/main.html')

@bp.route('/api/calculate', methods=['POST'])
def calculate():
    """API để tính toán kết quả"""
    data = request.json
    if not data or 'input' not in data:
        return jsonify({'error': 'Invalid input'}), 400
    
    # Sử dụng hàm từ module logic
    result = calculate_result(data['input'])
    return jsonify({'result': result})

# Hàm này sẽ được gọi từ app.py để thiết lập chương trình
def setup(app):
    """Thiết lập chương trình với Flask app"""
    # Đăng ký blueprint với ứng dụng Flask
    app.register_blueprint(bp)
    
    # In thông báo đã nạp thành công
    print(f"Đã nạp chương trình: {NAME}")
    
    # Các thiết lập khác nếu cần
    # - Khởi tạo database
    # - Thiết lập các task định kỳ
    # - Nạp cấu hình
    # - Vv... 