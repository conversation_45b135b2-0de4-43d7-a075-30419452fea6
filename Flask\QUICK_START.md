# 🚀 QUICK START - Basket Arrangement

## Bắt đầu nhanh trong 5 phút

### 1. Cài đặt Python dependencies
```bash
cd Flask
pip install -r requirements.txt
```

### 2. Chạy test để kiểm tra
```bash
python test_basket_arrangement.py
```

### 3. Khởi động Flask server
```bash
python app.py
```

### 4. Mở trình duyệt
Truy cập: `http://localhost:5000`

### 5. Sử dụng Basket Arrangement
1. Click "Basket Arrangement"
2. Nhập URL Google Spreadsheet
3. Click "Load Sheet"
4. Thêm điều kiện sắp xếp
5. Click "Xử lý sắp xếp"

## Ví dụ sử dụng

### Input mẫu:
- **Spreadsheet URL**: `https://docs.google.com/spreadsheets/d/YOUR_SHEET_ID/edit`
- **Top limit**: `Top 50`
- **Input IDs**: `ID001,ID002,ID003,ID004,ID005`
- **<PERSON>hung giờ**: `14:00-15:00`

### Kết quả:
- IDs được sắp xếp theo NO và Review
- Placement thông minh tôn trọng shop grouping
- Log chi tiết quá trình xử lý

## Tính năng nổi bật

✅ **Smart ID Input**: Tự động nhận diện format từ clipboard  
✅ **Multi-format support**: CSV, TSV, space-separated, line-separated  
✅ **Real-time validation**: Kiểm tra ID tồn tại trong Deal list  
✅ **NO order protection**: Đảm bảo thứ tự NO không bị vi phạm  
✅ **Shop grouping**: Tôn trọng nhóm shop liên tục  
✅ **Responsive UI**: Giao diện thân thiện trên mọi thiết bị  

## Troubleshooting nhanh

**❌ Lỗi import**: Cài đặt dependencies: `pip install -r requirements.txt`  
**❌ Lỗi Google Sheets**: Kiểm tra URL và quyền truy cập  
**❌ Lỗi validation**: Đảm bảo ID tồn tại trong Deal list  
**❌ Server không start**: Kiểm tra port 5000 có bị chiếm không  

## Liên hệ
Nếu cần hỗ trợ, kiểm tra file `README_BASKET_ARRANGEMENT.md` để biết chi tiết.

---
**Ready to use!** 🎉
