#!/usr/bin/env python3
"""
Test import để kiểm tra các module có hoạt động không
"""

try:
    print("Testing imports...")
    
    # Test import core modules
    from programs.basket_arrangement.core import constants
    print("✅ constants imported successfully")
    
    from programs.basket_arrangement.core import gsheet_manager
    print("✅ gsheet_manager imported successfully")
    
    from programs.basket_arrangement.core import deal_list_manager
    print("✅ deal_list_manager imported successfully")
    
    from programs.basket_arrangement.core import smart_placement
    print("✅ smart_placement imported successfully")
    
    from programs.basket_arrangement.core import time_slot_processor
    print("✅ time_slot_processor imported successfully")
    
    # Test import main logic
    from programs.basket_arrangement import logic
    print("✅ logic imported successfully")
    
    # Test import main module
    import programs.basket_arrangement
    print("✅ basket_arrangement module imported successfully")
    
    print("\n🎉 All imports successful!")
    print("Basket Arrangement is ready to use!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()
