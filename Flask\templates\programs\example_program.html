<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> trình mẫu</title>
    <style>
        /* CSS cho chương trình mẫu */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1a73e8;
            margin-top: 0;
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input[type="number"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #155db1;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background-color: #e6f4ea;
            border: 1px solid #ceead6;
            color: #1e8e3e;
            display: block;
        }
        .result.error {
            background-color: #fce8e6;
            border: 1px solid #f6cbc7;
            color: #d93025;
            display: block;
        }
        .history-section {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chương trình mẫu</h1>
        <p>Đây là một chương trình mẫu để minh họa cấu trúc làm việc với WebUI + Flask.</p>
        
        <div class="input-group">
            <label for="input-value">Nhập một số:</label>
            <input type="number" id="input-value" placeholder="Ví dụ: 10">
        </div>
        
        <button id="calculate-btn">Tính toán</button>
        
        <div id="result" class="result"></div>
        
        <div class="history-section">
            <h2>Lịch sử tính toán</h2>
            <table id="history-table">
                <thead>
                    <tr>
                        <th>Đầu vào</th>
                        <th>Kết quả</th>
                        <th>Thời gian</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Dữ liệu lịch sử sẽ được điền vào đây -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const inputValue = document.getElementById('input-value');
            const calculateBtn = document.getElementById('calculate-btn');
            const resultDiv = document.getElementById('result');
            const historyTable = document.getElementById('history-table').getElementsByTagName('tbody')[0];
            
            // Lấy lịch sử giả lập
            const historyData = [
                {input: '5', result: '10', timestamp: '2023-05-30 10:30:45'},
                {input: '10', result: '20', timestamp: '2023-05-30 10:31:12'},
                {input: '7.5', result: '15', timestamp: '2023-05-30 10:32:30'}
            ];
            
            // Hiển thị lịch sử
            historyData.forEach(item => {
                const row = historyTable.insertRow();
                row.insertCell(0).textContent = item.input;
                row.insertCell(1).textContent = item.result;
                row.insertCell(2).textContent = item.timestamp;
            });
            
            // Xử lý sự kiện tính toán
            calculateBtn.addEventListener('click', function() {
                const value = inputValue.value;
                
                if (!value) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = 'Vui lòng nhập một số';
                    return;
                }
                
                // Gọi API để tính toán
                fetch('/example_program/api/calculate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ input: value })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.result && data.result.status === 'success') {
                        // Hiển thị kết quả thành công
                        resultDiv.className = 'result success';
                        resultDiv.textContent = data.result.message;
                        
                        // Thêm vào lịch sử
                        const now = new Date();
                        const timestamp = now.toLocaleString();
                        
                        const row = historyTable.insertRow(0);
                        row.insertCell(0).textContent = value;
                        row.insertCell(1).textContent = data.result.value;
                        row.insertCell(2).textContent = timestamp;
                    } else {
                        // Hiển thị lỗi
                        resultDiv.className = 'result error';
                        resultDiv.textContent = data.result.message;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = 'Lỗi kết nối đến server: ' + error.message;
                });
            });
        });
    </script>
</body>
</html> 