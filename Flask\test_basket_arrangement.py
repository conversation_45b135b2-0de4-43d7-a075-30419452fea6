#!/usr/bin/env python3
"""
Test script cho Basket Arrangement
Kiểm tra các chức năng cơ bản của chương trình
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from programs.basket_arrangement.logic import (
    EnhancedBasketArrangementLogic,
    AdvancedSortingEngine,
    ValidationEngine
)
from programs.basket_arrangement.core import (
    DealListManager,
    SmartPlacementEngine,
    TimeSlotProcessor
)

def test_deal_list_manager():
    """Test DealListManager"""
    print("=== Test DealListManager ===")

    manager = DealListManager()

    # Test data mẫu
    test_data = {
        'ID001': {'shop': 'Shop A', 'gmv': 1000.0, 'review': True, 'no': 1},
        'ID002': {'shop': 'Shop A', 'gmv': 800.0, 'review': False, 'no': 1},
        'ID003': {'shop': 'Shop B', 'gmv': 1200.0, 'review': True, 'no': 2},
        'ID004': {'shop': 'Shop B', 'gmv': 900.0, 'review': True, 'no': 2},
    }

    # Simulate loading data
    for item_id, data in test_data.items():
        manager.id_to_shop[item_id] = data['shop']
        manager.id_to_gmv[item_id] = data['gmv']
        manager.id_to_review[item_id] = data['review']
        manager.id_to_no[item_id] = data['no']

        if data['shop'] not in manager.shop_to_ids:
            manager.shop_to_ids[data['shop']] = []
        manager.shop_to_ids[data['shop']].append(item_id)

    # Test methods
    print(f"ID001 has review: {manager.has_review('ID001')}")
    print(f"ID002 GMV: {manager.get_gmv('ID002')}")
    print(f"ID003 NO: {manager.get_no('ID003')}")
    print(f"Shop A IDs: {manager.shop_to_ids.get('Shop A', [])}")

    print("✅ DealListManager test passed\n")

def test_advanced_sorting_engine():
    """Test AdvancedSortingEngine"""
    print("=== Test AdvancedSortingEngine ===")

    # Setup
    manager = DealListManager()
    test_data = {
        'ID001': {'shop': 'Shop A', 'gmv': 1000.0, 'review': True, 'no': 2},
        'ID002': {'shop': 'Shop A', 'gmv': 800.0, 'review': False, 'no': 1},
        'ID003': {'shop': 'Shop B', 'gmv': 1200.0, 'review': True, 'no': 1},
        'ID004': {'shop': 'Shop B', 'gmv': 900.0, 'review': True, 'no': 2},
    }

    for item_id, data in test_data.items():
        manager.id_to_shop[item_id] = data['shop']
        manager.id_to_gmv[item_id] = data['gmv']
        manager.id_to_review[item_id] = data['review']
        manager.id_to_no[item_id] = data['no']

    # Test sorting
    engine = AdvancedSortingEngine(manager, print)
    id_list = ['ID001', 'ID002', 'ID003', 'ID004']

    print(f"Original order: {id_list}")
    sorted_list = engine.sort_ids_by_no_and_review(id_list)
    print(f"Sorted order: {sorted_list}")

    # Expected: NO 1 first (ID003 review+high GMV, ID002 no review), then NO 2 (ID001 review, ID004 review+lower GMV)
    expected = ['ID003', 'ID002', 'ID001', 'ID004']
    if sorted_list == expected:
        print("✅ Sorting test passed")
    else:
        print(f"❌ Sorting test failed. Expected: {expected}, Got: {sorted_list}")

    print()

def test_validation_engine():
    """Test ValidationEngine"""
    print("=== Test ValidationEngine ===")

    # Setup
    manager = DealListManager()
    manager.id_to_shop = {'ID001': 'Shop A', 'ID002': 'Shop B'}
    manager.id_to_no = {'ID001': 1, 'ID002': 2}

    engine = ValidationEngine(manager, print)

    # Test valid list
    valid_list = ['ID001', 'ID002']
    is_valid, msg = engine.validate_id_list(valid_list)
    print(f"Valid list test: {is_valid}, {msg}")

    # Test invalid list
    invalid_list = ['ID001', 'ID999']
    is_valid, msg = engine.validate_id_list(invalid_list)
    print(f"Invalid list test: {is_valid}, {msg}")

    # Test NO order
    ordered_list = ['ID001', 'ID002']  # NO 1, NO 2
    is_ordered, msg = engine.check_no_order_integrity(ordered_list)
    print(f"Ordered list test: {is_ordered}, {msg}")

    unordered_list = ['ID002', 'ID001']  # NO 2, NO 1
    is_ordered, msg = engine.check_no_order_integrity(unordered_list)
    print(f"Unordered list test: {is_ordered}, {msg}")

    print("✅ ValidationEngine test passed\n")

def test_smart_placement_engine():
    """Test SmartPlacementEngine"""
    print("=== Test SmartPlacementEngine ===")

    # Setup
    manager = DealListManager()
    manager.id_to_shop = {
        'ID001': 'Shop A', 'ID002': 'Shop A', 'ID003': 'Shop B',
        'ID004': 'Shop B', 'ID005': 'Shop C'
    }

    engine = SmartPlacementEngine(manager, print)

    # Test structure analysis
    current_ids = ['ID001', 'ID002', '', 'ID003', 'ID004']
    structure, groups = engine.analyze_current_structure(current_ids)

    print(f"Empty positions: {structure['empty_positions']}")
    print(f"Shop groups: {structure['shop_groups']}")
    print(f"Boundary positions: {structure['boundary_positions']}")

    # Test optimal positions
    input_ids = ['ID005']
    top_limits = {'ID005': 10}
    is_grouped_flags = {'ID005': False}

    placements = engine.find_optimal_positions(
        current_ids, input_ids, top_limits, is_grouped_flags
    )

    print(f"Optimal placements: {placements}")
    print("✅ SmartPlacementEngine test passed\n")

def test_enhanced_logic():
    """Test EnhancedBasketArrangementLogic"""
    print("=== Test EnhancedBasketArrangementLogic ===")

    logic = EnhancedBasketArrangementLogic()

    # Test URL extraction
    test_urls = [
        "https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit",
        "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "key=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
    ]

    for url in test_urls:
        try:
            sheet_id = logic._extract_sheet_id(url)
            print(f"URL: {url[:50]}... -> ID: {sheet_id}")
        except Exception as e:
            print(f"URL: {url[:50]}... -> Error: {e}")

    # Test column mapping
    mapping = logic.get_default_column_mapping()
    print(f"Default mapping: {mapping}")

    print("✅ EnhancedBasketArrangementLogic test passed\n")

def run_all_tests():
    """Chạy tất cả tests"""
    print("🧺 BASKET ARRANGEMENT - UNIT TESTS")
    print("=" * 50)

    try:
        test_deal_list_manager()
        test_advanced_sorting_engine()
        test_validation_engine()
        test_smart_placement_engine()
        test_enhanced_logic()

        print("🎉 ALL TESTS PASSED!")
        print("Chương trình Basket Arrangement đã sẵn sàng sử dụng.")

    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_tests()
