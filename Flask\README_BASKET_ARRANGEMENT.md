# 🧺 Basket Arrangement - Hướng dẫn cài đặt và sử dụng

## Tổng quan
Basket Arrangement là một chương trình Flask tích hợp đầy đủ logic từ `basket_package.py` và `gsheet_manager.py` để sắp xếp ID thông minh vào Google Sheet với thuật toán tối ưu.

## Tính năng chính

### ✨ Thuật toán sắp xếp v2.0
- **Thứ tự NO "bất khả xâm phạm"**: ID có NO 1 luôn được xếp đầu tiên
- **Ưu tiên Review**: Trong cùng NO, ID có review được xếp trước
- **Sắp xếp theo GMV**: Trong cùng NO và review, sắp xếp theo GMV giảm dần

### 🎯 Smart Placement Engine
- **Phân tích cấu trúc**: Tự động phân tích vị trí trống, boundary, nhóm shop
- **Placement thông minh**: Ưu tiên vị trí trống, boundary, edge, middle
- **Tôn trọng shop grouping**: Không phá vỡ nhóm shop liên tục

### 🔧 Validation Engine
- **Kiểm tra ID**: Validation ID có tồn tại trong Deal list
- **Kiểm tra thứ tự NO**: Đảm bảo thứ tự NO được duy trì
- **Auto-fix**: Tự động sửa chữa thứ tự NO nếu bị vi phạm

### 🌐 Giao diện Web hiện đại
- **Responsive design**: Tương thích mọi thiết bị
- **Real-time log**: Hiển thị log xử lý theo thời gian thực
- **Multi-condition**: Hỗ trợ nhiều điều kiện sắp xếp
- **Smart input**: Tự động nhận diện và format ID từ nhiều nguồn

## Cài đặt

### 1. Yêu cầu hệ thống
- Python 3.7+
- pip (Python package manager)

### 2. Cài đặt dependencies
```bash
cd Flask
pip install -r requirements.txt
```

### 3. Cấu trúc thư mục
```
Flask/
├── app.py                          # Flask app chính
├── gsheet_manager.py              # Google Sheets manager (độc lập)
├── basket_package.py              # Logic gốc từ PyQt6
├── requirements.txt               # Dependencies
├── programs/
│   └── basket_arrangement/
│       ├── __init__.py           # Blueprint và API endpoints
│       ├── logic.py              # Logic xử lý chính
│       └── static/
│           └── css/
│               └── basket_style.css  # CSS riêng
├── templates/
│   └── programs/
│       └── basket_arrangement.html   # Template HTML
└── static/
    ├── css/
    │   └── style.css             # CSS chung
    └── js/
        └── basket_arrangement.js # JavaScript logic
```

## Chạy chương trình

### 1. Khởi động Flask server
```bash
cd Flask
python app.py
```

### 2. Truy cập giao diện web
Mở trình duyệt và truy cập: `http://localhost:5000`

### 3. Sử dụng Basket Arrangement
1. Click vào "Basket Arrangement" từ trang chủ
2. Nhập URL Google Spreadsheet
3. Click "Load Sheet" để tải thông tin sheet
4. Thêm điều kiện sắp xếp
5. Click "Xử lý sắp xếp" để thực hiện

## Hướng dẫn sử dụng chi tiết

### 1. Thiết lập Google Sheet
- **Spreadsheet URL**: Nhập URL đầy đủ của Google Spreadsheet
- **Sheet selection**: Chọn sheet "Basket" (hoặc sheet khác)
- **Cột ID đầu**: Cột bắt đầu (mặc định: A)
- **Số cột**: Số lượng khung giờ (mặc định: 12)

### 2. Thêm điều kiện sắp xếp
- **Top limit**: Chọn giới hạn top (20, 30, 50, 100, 150, 200, 250)
- **Input IDs**: Nhập hoặc dán danh sách ID (hỗ trợ nhiều định dạng)
- **Nhóm**: Đánh dấu nếu các ID thuộc cùng thương hiệu
- **Khung giờ**: Chọn một hoặc nhiều khung giờ áp dụng

### 3. Công cụ hỗ trợ
- **📋 Paste**: Dán trực tiếp từ clipboard
- **🔄 Dedup**: Loại bỏ ID trùng lặp
- **Map cột**: Thiết lập ánh xạ cột Deal list

### 4. Theo dõi kết quả
- **Log real-time**: Xem quá trình xử lý chi tiết
- **Thống kê**: Số lượng ID trước/sau, số ID được thêm
- **Validation**: Kiểm tra tính hợp lệ và thứ tự NO

## Cấu hình nâng cao

### 1. OAuth2 Credentials
File `gsheet_manager.py` sử dụng OAuth2 để kết nối Google Sheets. Credentials được mã hóa Base64 trong `BASE64_OAUTH`.

### 2. Column Mapping
Mặc định mapping cột Deal list:
- **Shop ID**: Cột A
- **Item ID**: Cột B  
- **GMV**: Cột C
- **Review**: Cột D
- **NO**: Cột E

### 3. Thuật toán parameters
```python
MAX_UNIQUE_IDS = 500              # Số ID tối đa mỗi cột
MIN_DISTANCE_BETWEEN_INPUT_IDS = 2 # Khoảng cách tối thiểu
PLACEMENT_PRIORITY_WEIGHTS = {
    'EMPTY_SLOT': 0.7,            # 70% ưu tiên vị trí trống
    'BOUNDARY': 0.2,              # 20% ưu tiên boundary
    'GROUP_EDGE': 0.08,           # 8% ưu tiên edge
    'GROUP_MIDDLE': 0.02          # 2% ưu tiên middle
}
```

## API Endpoints

### 1. Load Sheet
```
POST /basket_arrangement/api/load_sheet
Body: {"sheet_url": "https://docs.google.com/spreadsheets/..."}
```

### 2. Get Time Slots
```
POST /basket_arrangement/api/get_time_slots
Body: {"sheet_name": "Basket", "first_col": "A", "num_columns": 12}
```

### 3. Process Arrangement
```
POST /basket_arrangement/api/process_arrangement
Body: {
    "conditions": [...],
    "sheet_config": {...}
}
```

## Troubleshooting

### 1. Lỗi kết nối Google Sheets
- Kiểm tra URL Spreadsheet có đúng không
- Đảm bảo có quyền truy cập sheet
- Kiểm tra OAuth2 credentials

### 2. Lỗi validation ID
- Kiểm tra ID có tồn tại trong Deal list không
- Đảm bảo mapping cột đúng
- Kiểm tra format ID input

### 3. Lỗi thứ tự NO
- Chương trình tự động sửa chữa thứ tự NO
- Kiểm tra dữ liệu NO trong Deal list
- Xem log để biết chi tiết

## Mở rộng

### 1. Thêm chương trình mới
Tạo thư mục mới trong `programs/` với cấu trúc tương tự `basket_arrangement/`

### 2. Tùy chỉnh thuật toán
Chỉnh sửa các class trong `logic.py`:
- `SmartPlacementEngine`: Thuật toán placement
- `AdvancedSortingEngine`: Thuật toán sắp xếp
- `ValidationEngine`: Validation logic

### 3. Tùy chỉnh giao diện
- Chỉnh sửa `basket_arrangement.html` cho template
- Cập nhật `basket_style.css` cho styling
- Mở rộng `basket_arrangement.js` cho logic frontend

## Liên hệ hỗ trợ
Nếu gặp vấn đề, vui lòng kiểm tra:
1. Log trong giao diện web
2. Console log của trình duyệt
3. Terminal output của Flask server

---
**Phiên bản**: 2.0  
**Ngày cập nhật**: 2024  
**Tác giả**: Augment Agent
