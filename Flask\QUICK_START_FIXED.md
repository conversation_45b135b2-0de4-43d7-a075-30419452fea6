# 🚀 QUICK START - Basket Arrangement (FIXED)

## ✅ **CÁC LỖI ĐÃ ĐƯỢC SỬA**

1. **Lỗi `before_first_request`**: ✅ Đã sửa trong `app.py` - Flask 2.2+ không hỗ trợ decorator này
2. **Lỗi template syntax**: ✅ Đã sửa conflict giữa Jinja2 và JavaScript trong `templates/index.html`
3. **Chương trình mẫu**: ✅ Đã xóa `programs/example_program/` gây conflict

## 🔧 **CÁCH CHẠY CHƯƠNG TRÌNH**

### Bước 1: Ki<PERSON>m tra Python
```bash
python --version
# Cần Python 3.7+ và pip
```

### Bước 2: Cài đặt dependencies
```bash
cd Flask
pip install -r requirements.txt
```

### Bước 3: Test import (tùy chọn)
```bash
python test_import.py
# Kiểm tra tất cả module có import được không
```

### Bước 4: Test Flask cơ bản (tù<PERSON> chọ<PERSON>)
```bash
python test_flask.py
# Truy cập http://localhost:5001 để test Flask hoạt động
```

### Bước 5: Chạy ứng dụng chính
```bash
python app.py
```

### Bước 6: Truy cập ứng dụng
- Mở trình duyệt
- Truy cập: `http://localhost:5000`
- Bạn sẽ thấy giao diện chính với nút "Basket Arrangement"

### Bước 7: Sử dụng Basket Arrangement
1. Click nút "Basket Arrangement"
2. Nhập URL Google Spreadsheet
3. Click "Load Sheet"
4. Chọn sheet và cấu hình
5. Thêm điều kiện sắp xếp
6. Click "Xử lý sắp xếp"

## 🐛 **TROUBLESHOOTING**

### Nếu gặp lỗi Python không tìm thấy:
```bash
# Windows - Cài Python từ python.org hoặc Microsoft Store
# Hoặc sử dụng py thay vì python:
py app.py
```

### Nếu gặp lỗi import module:
```bash
# Kiểm tra đang ở đúng thư mục Flask
cd Flask
python test_import.py
```

### Nếu gặp lỗi dependencies:
```bash
# Cài đặt lại dependencies
pip install --upgrade -r requirements.txt
```

### Nếu Flask không start:
```bash
# Kiểm tra port 5000 có bị chiếm không
# Hoặc thay đổi port trong app.py:
# app.run(debug=True, port=5001)
```

## 📁 **CẤU TRÚC HOÀN CHỈNH**

```
Flask/
├── app.py                          # ✅ Flask app chính (đã sửa lỗi)
├── requirements.txt               # Dependencies
├── test_import.py                 # Test import modules
├── test_flask.py                  # Test Flask cơ bản
├── programs/
│   └── basket_arrangement/        # ✅ Chương trình chính
│       ├── __init__.py           # Blueprint
│       ├── logic.py              # Logic xử lý
│       └── core/                 # Core modules
│           ├── constants.py      # Hằng số
│           ├── gsheet_manager.py # Google Sheets
│           ├── deal_list_manager.py
│           ├── smart_placement.py
│           └── time_slot_processor.py
├── templates/
│   ├── index.html                # ✅ Trang chính (đã sửa lỗi)
│   └── programs/
│       └── basket_arrangement.html
└── static/
    ├── css/
    └── js/
        └── basket_arrangement.js
```

## 🎉 **READY TO USE!**

Chương trình **Basket Arrangement** đã sẵn sàng sử dụng với:
- ✅ Cấu trúc modular chuyên nghiệp
- ✅ Logic 100% từ desktop app
- ✅ Giao diện web hiện đại
- ✅ Tất cả lỗi đã được sửa

**Enjoy coding!** 🚀
