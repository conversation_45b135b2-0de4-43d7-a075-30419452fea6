/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f5f5;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Header Styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #1a73e8;
    color: white;
    padding: 10px 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.logo {
    display: flex;
    align-items: center;
}

.program-list-button {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    margin-right: 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.program-list-button:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 500;
}

/* Program List Styles */
.program-list {
    position: absolute;
    top: 60px;
    left: 20px;
    width: 350px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    overflow: hidden;
}

.program-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

.program-list-header h3 {
    font-size: 1.1rem;
    font-weight: 500;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 1.2rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: #e0e0e0;
}

.program-items {
    max-height: 500px;
    overflow-y: auto;
    padding: 10px;
}

.program-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.program-item:hover {
    background-color: #f0f7ff;
}

.program-item img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
    margin-right: 15px;
}

.program-info h4 {
    font-size: 1rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.program-info p {
    font-size: 0.9rem;
    color: #666;
}

.no-programs {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Main Content Styles */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Tab Bar Styles */
.tab-bar {
    display: flex;
    background-color: #f0f0f0;
    border-bottom: 1px solid #ddd;
    height: 40px;
    overflow-x: auto;
    white-space: nowrap;
}

.tab {
    display: flex;
    align-items: center;
    padding: 0 15px;
    height: 100%;
    border-right: 1px solid #ddd;
    background-color: #f0f0f0;
    min-width: 150px;
    max-width: 200px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.tab.active {
    background-color: white;
    border-bottom: 2px solid #1a73e8;
}

.tab span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.9rem;
}

.tab-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    line-height: 1;
    width: 20px;
    height: 20px;
    text-align: center;
    border-radius: 50%;
    cursor: pointer;
    margin-left: 5px;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tab-close:hover {
    background-color: #ddd;
}

/* Tab Content Styles */
.tab-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.tab-pane {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    display: none;
}

.tab-pane.active {
    display: block;
}

.program-frame {
    width: 100%;
    height: 100%;
    border: none;
}

/* Welcome Screen Styles */
.welcome-screen {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: white;
}

.welcome-content {
    text-align: center;
    max-width: 600px;
    padding: 20px;
}

.welcome-content h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 15px;
}

.welcome-content p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

/* Footer Styles */
footer {
    background-color: #f0f0f0;
    border-top: 1px solid #ddd;
    padding: 5px 20px;
    font-size: 0.9rem;
    color: #666;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .program-list {
        width: 90%;
        left: 5%;
    }
    
    .logo h1 {
        font-size: 1.2rem;
    }
} 