# <PERSON><PERSON><PERSON> hàm xử lý logic của chương trình mẫu

def calculate_result(input_value):
    """
    <PERSON><PERSON>m thực hiện logic xử lý của chương trình mẫu
    Đây là nơi chuyển đổi từ logic PyQt6 sang logic cho web UI
    """
    try:
        # Ví dụ đơn giản: nhân đầu vào với 2
        result = float(input_value) * 2
        return {
            'status': 'success',
            'value': result,
            'message': f'Kết quả: {input_value} × 2 = {result}'
        }
    except ValueError:
        return {
            'status': 'error',
            'message': 'Đầu vào phải là một số'
        }
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Lỗi xử lý: {str(e)}'
        }

# <PERSON><PERSON><PERSON> h<PERSON><PERSON> bổ sung cho chương trình mẫu
def get_history():
    """<PERSON><PERSON><PERSON> lập lấy lịch sử tính toán"""
    return [
        {'input': 5, 'result': 10, 'timestamp': '2023-05-30 10:30:45'},
        {'input': 10, 'result': 20, 'timestamp': '2023-05-30 10:31:12'},
        {'input': 7.5, 'result': 15, 'timestamp': '2023-05-30 10:32:30'},
    ]

def save_result(input_value, result):
    """Giả lập lưu kết quả vào lịch sử"""
    # Trong ứng dụng thực tế, đây sẽ là nơi lưu dữ liệu vào database
    print(f"Đã lưu kết quả: input={input_value}, result={result}")
    return True 