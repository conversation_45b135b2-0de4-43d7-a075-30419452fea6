from flask import Blueprint, render_template, request, jsonify, session
from .logic import EnhancedBasketArrangementLogic
import traceback

# Thông tin chương trình
NAME = "Basket Arrangement"
DESCRIPTION = "Sắp xếp ID thông minh vào Google Sheet với thuật toán tối ưu"
ICON = "basket-icon.png"

# Tạo Blueprint
basket_bp = Blueprint('basket_arrangement', __name__,
                     template_folder='templates',
                     static_folder='static',
                     url_prefix='/basket_arrangement')

def setup(app):
    """Đăng ký blueprint với Flask app"""
    app.register_blueprint(basket_bp)

@basket_bp.route('/')
def index():
    """Trang chính của Basket Arrangement"""
    return render_template('programs/basket_arrangement.html')

@basket_bp.route('/api/load_sheet', methods=['POST'])
def load_sheet():
    """API để load Google Sheet"""
    try:
        data = request.get_json()
        sheet_url = data.get('sheet_url', '')

        # Khởi tạo logic processor
        if 'basket_logic' not in session:
            session['basket_logic'] = {}

        logic = EnhancedBasketArrangementLogic()
        result = logic.load_spreadsheet(sheet_url)

        # Lưu thông tin sheet vào session
        session['basket_logic']['sheet_info'] = result
        session.modified = True

        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@basket_bp.route('/api/get_time_slots', methods=['POST'])
def get_time_slots():
    """API để lấy danh sách khung giờ từ sheet"""
    try:
        data = request.get_json()
        sheet_name = data.get('sheet_name', 'Basket')
        first_col = data.get('first_col', 'A')
        num_columns = data.get('num_columns', 12)

        logic = EnhancedBasketArrangementLogic()
        time_slots = logic.get_time_slots(sheet_name, first_col, num_columns)

        return jsonify({
            'success': True,
            'time_slots': time_slots
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@basket_bp.route('/api/process_arrangement', methods=['POST'])
def process_arrangement():
    """API để xử lý sắp xếp ID"""
    try:
        data = request.get_json()
        conditions = data.get('conditions', [])
        sheet_config = data.get('sheet_config', {})

        logic = EnhancedBasketArrangementLogic()
        result = logic.process_arrangement(conditions, sheet_config)

        return jsonify({
            'success': True,
            'result': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@basket_bp.route('/api/get_column_mapping', methods=['GET'])
def get_column_mapping():
    """API để lấy thông tin mapping cột"""
    try:
        logic = EnhancedBasketArrangementLogic()
        mapping = logic.get_default_column_mapping()

        return jsonify({
            'success': True,
            'mapping': mapping
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@basket_bp.route('/api/update_column_mapping', methods=['POST'])
def update_column_mapping():
    """API để cập nhật mapping cột"""
    try:
        data = request.get_json()
        mapping = data.get('mapping', {})

        # Lưu mapping vào session
        if 'basket_logic' not in session:
            session['basket_logic'] = {}
        session['basket_logic']['column_mapping'] = mapping
        session.modified = True

        return jsonify({
            'success': True,
            'message': 'Đã cập nhật mapping cột thành công'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
