# WebUI + Flask Multi-Program

Hệ thống chạy nhiều chương trình trên nền WebUI + Flask, thay thế cho PyQt6.

## Tính năng chính

- Hỗ trợ 8 chương trình con chạy đồng thời trên các tab
- Chuyển tab không làm mất trạng thái của các chương trình
- Giao diện người dùng thân thiện và hiện đại

## Cấu trúc thư mục

```
Flask/
├── app.py                # Flask application chính
├── run.bat               # File batch để khởi động ứng dụng dễ dàng
├── requirements.txt      # Dependencies
├── static/               # Static files (CSS, JS, images)
│   ├── css/
│   ├── js/
│   └── img/
├── templates/            # HTML templates
│   ├── index.html        # Trang chủ
│   └── programs/         # Templates cho các chương trình
├── programs/             # Th<PERSON> mục chứa logic cho các chương trình
│   ├── program1/
│   ├── program2/
│   └── ...
└── utils/                # Các utility functions chung
```

## Hướng dẫn cài đặt

1. Cài đặt Python 3.8 trở lên
2. Cài đặt các dependencies:
   ```
   pip install -r requirements.txt
   ```

## Hướng dẫn sử dụng

1. Chạy file `run.bat` để khởi động ứng dụng
2. Hoặc mở terminal và chạy lệnh:
   ```
   python app.py
   ```
3. Mở trình duyệt và truy cập địa chỉ http://localhost:5000

## Hướng dẫn thêm chương trình mới

1. Tạo thư mục mới trong `programs/` với cấu trúc sau:
   ```
   programs/ten_chuong_trinh/
   ├── __init__.py        # Khai báo thông tin và routes
   ├── logic.py           # Logic xử lý
   └── utils.py           # (Tùy chọn) Các hàm utility
   ```

2. Tạo template cho chương trình trong `templates/programs/`:
   ```
   templates/programs/ten_chuong_trinh.html
   ```

3. Thêm thông tin chương trình trong `programs/ten_chuong_trinh/__init__.py`:
   ```python
   NAME = "Tên chương trình"
   DESCRIPTION = "Mô tả ngắn về chương trình"
   ICON = "icon_name.png"  # Đặt icon trong static/img/
   ```

4. Xem chương trình mẫu trong `programs/example_program/` để tham khảo

## Liên hệ

Nếu có vấn đề hoặc câu hỏi, vui lòng liên hệ với quản trị viên. 